export interface ReportConfig {
  name: string;
  description: string;
  documentName: string;
  useEnglishFields: boolean;
  includeObservations: boolean;
}

export interface ApiReport {
  id: number;
  name: string;
  description: string;
  document_name: string;
  use_english_fields: boolean;
  include_observations: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ReportField {
  id: number;
  name: string;
  type: string;
  subphase: {
    id: number;
    name: string;
    phase: {
      id: number;
      name: string;
    };
  };
}

export interface FieldTypeFilter {
  statuses: ("completed" | "not_completed" | "in_progress")[];
  includeObservations: boolean;
}

export interface ReportFilter {
  field_type: string;
  filter_options: ("completed" | "not_completed" | "in_progress")[];
  include_observations: boolean;
}

export interface ReportDetailResponse {
  id: number;
  name: string;
  description: string;
  document_name: string;
  use_english_fields: boolean;
  include_observations: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  full_definition: {
    fields: ReportField[];
    filters: ReportFilter[];
  };
}

export interface CreateReportData {
  reportConfig: ReportConfig;
  selectedFields: number[];
  fieldFilters: Record<string, FieldTypeFilter>;
}

export interface UpdateReportData {
  name?: string;
  description?: string;
  document_name?: string;
  use_english_fields?: boolean;
  include_observations?: boolean;
  selectedFields?: number[];
  fieldFilters?: Record<string, FieldTypeFilter>;
}

export interface ReportData {
  name: string;
  description: string;
  documentName: string;
  useEnglishFields: boolean;
  includeObservations: boolean;
}
