import React from "react";
import { Icon } from "@iconify/react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ooter,
  ModalBody,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Progress,
} from "@heroui/react";
import { ApiProjectResponse } from "@/types/project-api";

export interface VerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  projectData?: ApiProjectResponse,
}

interface Phase {
  name: string;
  completed: boolean;
  verified: boolean;
  percentage: number;
}

export const VerificationModal: React.FC<VerificationModalProps> = ({
  isOpen,
  onClose,
  projectData,
}) => {
  const handleVerify = (index: number) => {
    setPhases((prevPhases) =>
      prevPhases.map((phase, i) =>
        i === index ? { ...phase, verified: !phase.verified } : phase,
      ),
    );
  };

  return (
    <Modal isOpen={isOpen} size="3xl" onClose={onClose}>
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1">
              Verificación de Fases
            </ModalHeader>
            <ModalBody>
              <Table removeWrapper aria-label="Tabla de verificación de fases">
                <TableHeader>
                  <TableColumn>FASE</TableColumn>
                  <TableColumn>VERIFICADO</TableColumn>
                  <TableColumn>PROGRESO</TableColumn>
                  <TableColumn>ACCIÓN</TableColumn>
                </TableHeader>
                <TableBody>
                  {phases.map((phase, index) => (
                    <TableRow key={phase.name}>
                      <TableCell>{phase.name}</TableCell>
                      <TableCell>
                        <span
                          className={`flex items-center gap-1 ${phase.verified ? "text-success" : "text-danger"}`}
                        >
                          <Icon
                            icon={phase.verified ? "lucide:check" : "lucide:x"}
                          />
                          {phase.verified ? "Verificado" : "No Verificado"}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress
                            className="max-w-md"
                            color={
                              phase.percentage === 100 ? "success" : "warning"
                            }
                            size="sm"
                            value={phase.percentage}
                          />
                          <span className="text-sm">{phase.percentage}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          className="w-full"
                          color={phase.verified ? "default" : "primary"}
                          size="sm"
                          onPress={() => handleVerify(index)}
                        >
                          {phase.verified ? "Desverificar" : "Verificar"}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ModalBody>
            <ModalFooter>
              <Button color="danger" variant="light" onPress={onClose}>
                Cerrar
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};
